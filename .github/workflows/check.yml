name: Check
on:
  workflow_dispatch:
  pull_request:
  push:
jobs:
  codeql:
    name: CodeQL
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          lfs: true
      - name: JDK<PERSON>
        uses: actions/setup-java@v3
        with:
          java-version: 8.0.312
          distribution: liberica
      - name: Setup Gradle
        uses: gradle/gradle-build-action@v2
      - name: Build
        run: ./gradlew build
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: artifact
          path: build/libs/*.jar