name: Game
env:
  itchio_project: aehmttw/Tanks
  itchio_channel: Universal_JAR
on:
  workflow_dispatch:
  push:
    tags:
      - v*
jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          lfs: true
      - name: JD<PERSON><PERSON>
        uses: actions/setup-java@v3
        with:
          java-version: 8.0.312
          distribution: liberica
      - name: Setup Gradle
        uses: gradle/gradle-build-action@v2
      - name: Build
        run: ./gradlew build
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: artifact
          path: build/libs/*.jar