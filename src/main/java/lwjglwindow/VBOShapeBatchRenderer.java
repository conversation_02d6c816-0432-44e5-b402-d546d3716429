package lwjglwindow;

import basewindow.BaseShapeBatchRenderer;
import basewindow.IBatchRenderableObject;
import basewindow.ShaderGroup;
import basewindow.transformation.Rotation;
import basewindow.transformation.Scale;
import basewindow.transformation.Translation;
import it.unimi.dsi.fastutil.objects.Object2IntLinkedOpenHashMap;
import it.unimi.dsi.fastutil.objects.Object2IntOpenHashMap;
import it.unimi.dsi.fastutil.objects.Object2ObjectOpenHashMap;
import org.lwjgl.BufferUtils;
import org.lwjgl.opengl.GL15;

import java.nio.Buffer;
import java.nio.FloatBuffer;
import java.util.ArrayList;
import java.util.HashMap;

import static org.lwjgl.opengl.GL11.*;

public class VBOShapeBatchRenderer extends BaseShapeBatchRenderer
{
    // Property classes for parallel arrays
    public static class AttributeProperty {
        public int vboId;
        public FloatBuffer buffer;
        public float[] floatArray;

        public AttributeProperty(int vboId, FloatBuffer buffer, float[] floatArray) {
            this.vboId = vboId;
            this.buffer = buffer;
            this.floatArray = floatArray;
        }
    }

    public static class BufferProperty {
        public int startPoint;
        public int size;

        public BufferProperty(int startPoint, int size) {
            this.startPoint = startPoint;
            this.size = size;
        }
    }

    public int vertVBO = -1, colVBO = -1;
    public int size = 0, capacity = 6000, initSize = 0;
    public boolean initialized = false;

    // Parallel arrays with ID maps
    public Object2IntOpenHashMap<ShaderGroup.Attribute> attributeToId = new Object2IntOpenHashMap<>();
    public ArrayList<AttributeProperty> attributeProperties = new ArrayList<>();
    public Object2IntLinkedOpenHashMap<IBatchRenderableObject> bufferToId = new Object2IntLinkedOpenHashMap<>();
    public ArrayList<BufferProperty> bufferProperties = new ArrayList<>();

    public FloatBuffer vertBuffer = BufferUtils.createFloatBuffer(capacity * 3);
    public FloatBuffer colBuffer = BufferUtils.createFloatBuffer(capacity * 4);

    public LWJGLWindow window;

    public float currentR, currentG, currentB, currentA;
    public float colorGlow;

    public boolean depth = false;
    public boolean glow = false;
    public boolean depthMask = false;

    public IBatchRenderableObject modifying = null;
    public int modifyingSize = -1;
    public int modifyingWritten = 0;

    public ShaderGroup shader;

    protected IBatchRenderableObject adding = null;
    protected boolean justExpanded = false;

    public VBOShapeBatchRenderer(LWJGLWindow window)
    {
        super(true);
        this.window = window;
        this.shader = window.currentShader.group;
    }

    public VBOShapeBatchRenderer(LWJGLWindow window, ShaderGroup s)
    {
        this(window);
        this.shader = s;

        for (ShaderGroup.Attribute a: s.attributes)
        {
            this.addAttribute(a);
        }
    }

    public void settings(boolean depth)
    {
        this.settings(depth, false);
    }

    public void settings(boolean depth, boolean glow)
    {
        this.settings(depth, glow, !(glow));
    }

    public void settings(boolean depth, boolean glow, boolean depthMask)
    {
        this.depth = depth;
        this.glow = glow;
        this.depthMask = depthMask;
    }

    public void draw()
    {
        glDepthMask(this.depthMask);

        if (this.depth)
        {
            window.enableDepthtest();
            glDepthFunc(GL_LEQUAL);
        }
        else
        {
            window.disableDepthtest();
            glDepthFunc(GL_ALWAYS);
        }

        if (this.glow)
            window.setGlowBlendFunc();
        else
            window.setTransparentBlendFunc();

        glMatrixMode(GL_MODELVIEW);
        glPushMatrix();
        Translation.transform(window, posX / window.absoluteWidth, posY / window.absoluteHeight, posZ / window.absoluteDepth);
        Rotation.transform(window, -pitch, -roll, -yaw);
        Scale.transform(window, sX, sY, sZ);

        this.batchDraw();

        glPopMatrix();

        window.disableDepthtest();
        glDepthMask(true);
        window.setTransparentBlendFunc();
    }

    @Override
    public void free()
    {
        this.window.freeVBO(this.colVBO);
        this.window.freeVBO(this.vertVBO);
    }

    public void setColor(float r, float g, float b, float a)
    {
        this.currentR = r / 255;
        this.currentG = g / 255;
        this.currentB = b / 255;
        this.currentA = a / 255;
    }

    public void setGlow(float g)
    {
        this.colorGlow = g;
    }

    public void expand()
    {
        this.vertBuffer.rewind();
        this.colBuffer.rewind();

        for (int i = 0; i < attributeProperties.size(); i++)
        {
            attributeProperties.get(i).buffer.rewind();
        }

        Object2IntLinkedOpenHashMap<IBatchRenderableObject> newBufferToId = new Object2IntLinkedOpenHashMap<>();

        int totalSize = 0;
        for (IBatchRenderableObject o : this.bufferToId.keySet())
            totalSize += this.bufferProperties.get(this.bufferToId.getInt(o)).size;

        int newCapacity = this.capacity * 2;

        if (totalSize <= this.capacity / 2)
            newCapacity = this.capacity;

        FloatBuffer newVertBuffer = BufferUtils.createFloatBuffer(newCapacity * 3);
        FloatBuffer newColBuffer = BufferUtils.createFloatBuffer(newCapacity * 4);
        ArrayList<FloatBuffer> newAttributeBuffers = new ArrayList<>();
        for (int i = 0; i < attributeProperties.size(); i++)
        {
            AttributeProperty prop = attributeProperties.get(i);
            newAttributeBuffers.add(BufferUtils.createFloatBuffer(newCapacity * prop.floatArray.length));
        }

        int pos = 0;
        int newPos = 0;
        for (IBatchRenderableObject o : this.bufferToId.keySet())
        {
            int bufferId = this.bufferToId.getInt(o);
            BufferProperty bufferProp = this.bufferProperties.get(bufferId);
            int start = bufferProp.startPoint;
            int size = bufferProp.size;

            while (pos < start)
            {
                this.vertBuffer.get();
                this.vertBuffer.get();
                this.vertBuffer.get();
                this.colBuffer.get();
                this.colBuffer.get();
                this.colBuffer.get();
                this.colBuffer.get();

                for (int i = 0; i < attributeProperties.size(); i++)
                {
                    AttributeProperty prop = attributeProperties.get(i);
                    for (int j = 0; j < prop.floatArray.length; j++)
                    {
                        prop.buffer.get();
                    }
                }
                pos++;
            }

            newBufferToId.put(o, bufferId);
            bufferProp.startPoint = newPos;

            while (pos < start + size)
            {
                newVertBuffer.put(this.vertBuffer.get());
                newVertBuffer.put(this.vertBuffer.get());
                newVertBuffer.put(this.vertBuffer.get());
                newColBuffer.put(this.colBuffer.get());
                newColBuffer.put(this.colBuffer.get());
                newColBuffer.put(this.colBuffer.get());
                newColBuffer.put(this.colBuffer.get());

                for (int i = 0; i < attributeProperties.size(); i++)
                {
                    AttributeProperty prop = attributeProperties.get(i);
                    for (int j = 0; j < prop.floatArray.length; j++)
                    {
                        newAttributeBuffers.get(i).put(prop.buffer.get());
                    }
                }

                pos++;
                newPos++;
            }
        }

        this.vertBuffer = newVertBuffer;
        this.colBuffer = newColBuffer;

        for (int i = 0; i < attributeProperties.size(); i++)
        {
            attributeProperties.get(i).buffer = newAttributeBuffers.get(i);
        }

        this.bufferToId = newBufferToId;
        this.size = newPos;
        this.capacity = newCapacity;

        this.justExpanded = true;
    }

    public void addPoint(float x, float y, float z)
    {
        IBatchRenderableObject o = this.adding;
        if (this.modifyingSize < 0)
        {
            if (this.size >= this.capacity)
                this.expand();
        }

        if (this.modifyingSize >= 0 && this.modifyingWritten >= this.modifyingSize)
            this.migrate(o);

        if (!this.bufferToId.containsKey(o))
        {
            int bufferId = this.bufferProperties.size();
            this.bufferToId.put(o, bufferId);
            this.bufferProperties.add(new BufferProperty(this.size, 1));
        }
        else if (this.modifyingSize < 0)
        {
            int bufferId = this.bufferToId.getInt(o);
            BufferProperty bufferProp = this.bufferProperties.get(bufferId);
            bufferProp.size++;
        }

        this.vertBuffer.put(x);
        this.vertBuffer.put(y);
        this.vertBuffer.put(z);
        this.colBuffer.put(this.currentR);
        this.colBuffer.put(this.currentG);
        this.colBuffer.put(this.currentB);
        this.colBuffer.put(this.currentA);

        for (int i = 0; i < attributeProperties.size(); i++)
        {
            AttributeProperty prop = attributeProperties.get(i);
            for (float f : prop.floatArray)
            {
                prop.buffer.put(f);
            }
        }

        if (this.modifyingSize < 0)
            this.size++;
        else
            this.modifyingWritten++;
    }

    public void endModification()
    {
        if (this.modifying == null || this.modifyingSize < 0 || !this.bufferToId.containsKey(this.modifying))
            return;

        int bufferId = this.bufferToId.getInt(this.modifying);
        BufferProperty bufferProp = this.bufferProperties.get(bufferId);
        int start = bufferProp.startPoint;
        for (int i = this.modifyingWritten + start; i < start + this.modifyingSize; i++)
        {
            this.vertBuffer.put(i * 3, 0f);
            this.vertBuffer.put(i * 3 + 1, 0f);
            this.vertBuffer.put(i * 3 + 2, 0f);

            this.colBuffer.put(i * 4, 0f);
            this.colBuffer.put(i * 4 + 1, 0f);
            this.colBuffer.put(i * 4 + 2, 0f);
            this.colBuffer.put(i * 4 + 3, 0f);

            for (int j = 0; j < attributeProperties.size(); j++)
            {
                AttributeProperty prop = attributeProperties.get(j);
                for (int o = 0; o < prop.floatArray.length; o++)
                {
                    prop.buffer.put(i * prop.floatArray.length + o, 0);
                }
            }
        }

        this.vertBuffer.position(start * 3);
        this.colBuffer.position(start * 4);
        this.vertBuffer.limit((start + this.modifyingSize) * 3);
        this.colBuffer.limit((start + this.modifyingSize) * 4);
        for (int i = 0; i < attributeProperties.size(); i++)
        {
            AttributeProperty prop = attributeProperties.get(i);
            prop.buffer.position(start * prop.floatArray.length);
            prop.buffer.limit((start + this.modifyingSize) * prop.floatArray.length);
        }

        GL15.glBindBuffer(GL15.GL_ARRAY_BUFFER, vertVBO);
        GL15.glBufferSubData(GL15.GL_ARRAY_BUFFER, (long) Float.BYTES * start * 3, this.vertBuffer);
        GL15.glBindBuffer(GL15.GL_ARRAY_BUFFER, colVBO);
        GL15.glBufferSubData(GL15.GL_ARRAY_BUFFER, (long) Float.BYTES * start * 4, this.colBuffer);
        for (int i = 0; i < attributeProperties.size(); i++)
        {
            AttributeProperty prop = attributeProperties.get(i);
            GL15.glBindBuffer(GL15.GL_ARRAY_BUFFER, prop.vboId);
            GL15.glBufferSubData(GL15.GL_ARRAY_BUFFER, (long) Float.BYTES * start * prop.floatArray.length, prop.buffer);
        }

        this.vertBuffer.limit(this.vertBuffer.capacity());
        this.colBuffer.limit(this.colBuffer.capacity());
        this.vertBuffer.position(this.size * 3);
        this.colBuffer.position(this.size * 4);
        for (int i = 0; i < attributeProperties.size(); i++)
        {
            AttributeProperty prop = attributeProperties.get(i);
            prop.buffer.limit(prop.buffer.capacity());
            prop.buffer.position(this.size * prop.floatArray.length);
        }

        this.modifying = null;
    }

    public void beginAdd(IBatchRenderableObject o)
    {
        if (this.adding != o)
            this.endAdd();

        this.adding = o;

        if (this.initialized)
        {
            this.vertBuffer.limit(this.capacity * 3);
            this.colBuffer.limit(this.capacity * 4);
            for (int i = 0; i < attributeProperties.size(); i++)
            {
                AttributeProperty prop = attributeProperties.get(i);
                prop.buffer.limit(this.capacity * prop.floatArray.length);
            }

            if (this.modifying != o)
            {
                this.endModification();
                this.modifyingWritten = 0;

                if (this.bufferToId.containsKey(o))
                {
                    int bufferId = this.bufferToId.getInt(o);
                    BufferProperty bufferProp = this.bufferProperties.get(bufferId);
                    this.modifyingSize = bufferProp.size;
                    this.vertBuffer.position(bufferProp.startPoint * 3);
                    this.colBuffer.position(bufferProp.startPoint * 4);
                    for (int i = 0; i < attributeProperties.size(); i++)
                    {
                        AttributeProperty prop = attributeProperties.get(i);
                        prop.buffer.position(bufferProp.startPoint * prop.floatArray.length);
                    }
                }
                else
                {
                    this.modifyingSize = -1;
                    this.vertBuffer.position(this.size * 3);
                    this.colBuffer.position(this.size * 4);
                    for (int i = 0; i < attributeProperties.size(); i++)
                    {
                        AttributeProperty prop = attributeProperties.get(i);
                        prop.buffer.position(this.size * prop.floatArray.length);
                    }
                }
            }

            this.modifying = o;
        }
    }

    public void endAdd()
    {
        if (this.adding == null)
            return;

        this.adding = null;

        if (this.justExpanded && this.initialized)
        {
            this.vertBuffer.flip();
            this.colBuffer.flip();

            this.vertBuffer.limit(this.vertBuffer.capacity());
            this.colBuffer.limit(this.colBuffer.capacity());
            this.window.vertexBufferDataDynamic(vertVBO, vertBuffer);
            this.window.vertexBufferDataDynamic(colVBO, colBuffer);

            for (int i = 0; i < attributeProperties.size(); i++)
            {
                AttributeProperty prop = attributeProperties.get(i);
                Buffer b = prop.buffer;
                b.flip();
                b.limit(b.capacity());

                this.window.vertexBufferDataDynamic(prop.vboId, b);
            }

            this.justExpanded = false;
            this.initSize = this.size;
        }
        else if (this.initSize < this.size && this.initialized)
        {
            this.vertBuffer.position(this.initSize * 3);
            this.colBuffer.position(this.initSize * 4);
            for (int i = 0; i < attributeProperties.size(); i++)
            {
                AttributeProperty prop = attributeProperties.get(i);
                prop.buffer.position(this.initSize * prop.floatArray.length);
            }

            this.vertBuffer.limit(this.size * 3);
            this.colBuffer.limit(this.size * 4);
            for (int i = 0; i < attributeProperties.size(); i++)
            {
                AttributeProperty prop = attributeProperties.get(i);
                prop.buffer.limit(this.size * prop.floatArray.length);
            }

            GL15.glBindBuffer(GL15.GL_ARRAY_BUFFER, vertVBO);
            GL15.glBufferSubData(GL15.GL_ARRAY_BUFFER, (long) Float.BYTES * this.initSize * 3, this.vertBuffer);
            GL15.glBindBuffer(GL15.GL_ARRAY_BUFFER, colVBO);
            GL15.glBufferSubData(GL15.GL_ARRAY_BUFFER, (long) Float.BYTES * this.initSize * 4, this.colBuffer);
            for (int i = 0; i < attributeProperties.size(); i++)
            {
                AttributeProperty prop = attributeProperties.get(i);
                GL15.glBindBuffer(GL15.GL_ARRAY_BUFFER, prop.vboId);
                GL15.glBufferSubData(GL15.GL_ARRAY_BUFFER, (long) Float.BYTES * this.initSize * prop.floatArray.length, prop.buffer);
            }

            this.initSize = this.size;
        }
    }

    public void delete(IBatchRenderableObject o)
    {
        if (!this.bufferToId.containsKey(o))
            return;

        if (this.modifying == o)
            this.modifying = null;

        if (this.adding != null)
            this.endAdd();

        int bufferId = this.bufferToId.removeInt(o);
        BufferProperty bufferProp = this.bufferProperties.get(bufferId);
        int pos = bufferProp.startPoint;
        int size = bufferProp.size;

        this.vertBuffer.position(pos * 3);
        this.colBuffer.position(pos * 4);
        for (int i = 0; i < attributeProperties.size(); i++)
        {
            AttributeProperty prop = attributeProperties.get(i);
            prop.buffer.position(pos * prop.floatArray.length);
        }

        for (int i = pos; i < pos + size; i++)
        {
            this.vertBuffer.put(i * 3, 0f);
            this.vertBuffer.put(i * 3 + 1, 0f);
            this.vertBuffer.put(i * 3 + 2, 0f);

            this.colBuffer.put(i * 4, 0f);
            this.colBuffer.put(i * 4 + 1, 0f);
            this.colBuffer.put(i * 4 + 2, 0f);
            this.colBuffer.put(i * 4 + 3, 0f);

            for (int j = 0; j < attributeProperties.size(); j++)
            {
                AttributeProperty prop = attributeProperties.get(j);
                for (int k = 0; k < prop.floatArray.length; k++)
                    prop.buffer.put(i * prop.floatArray.length + k, 0);
            }
        }


        GL15.glBindBuffer(GL15.GL_ARRAY_BUFFER, vertVBO);
        GL15.glBufferSubData(GL15.GL_ARRAY_BUFFER, (long) Float.BYTES * pos * 3, new float[3 * size]);
        GL15.glBindBuffer(GL15.GL_ARRAY_BUFFER, colVBO);
        GL15.glBufferSubData(GL15.GL_ARRAY_BUFFER, (long) Float.BYTES * pos * 4, new float[4 * size]);
        for (int i = 0; i < attributeProperties.size(); i++)
        {
            AttributeProperty prop = attributeProperties.get(i);
            GL15.glBindBuffer(GL15.GL_ARRAY_BUFFER, prop.vboId);
            GL15.glBufferSubData(GL15.GL_ARRAY_BUFFER, (long) Float.BYTES * pos * prop.floatArray.length, new float[prop.floatArray.length * size]);
        }

        this.vertBuffer.rewind();
        this.colBuffer.rewind();
        for (int i = 0; i < attributeProperties.size(); i++)
        {
            attributeProperties.get(i).buffer.rewind();
        }
    }

    public void moveFloat(FloatBuffer b, int mul, int off, int rem)
    {
        b.put(this.size * mul + off,  b.get(rem * mul + off));
        b.put(rem * mul + off, 0f);
    }

    public void migrate(IBatchRenderableObject o)
    {
        int bufferId = this.bufferToId.getInt(o);
        BufferProperty bufferProp = this.bufferProperties.get(bufferId);

        if (this.capacity <= this.size + bufferProp.size)
            this.expand();

        int pos = bufferProp.startPoint;
        int size = bufferProp.size;

        this.vertBuffer.position(pos * 3);
        this.colBuffer.position(pos * 4);
        for (int i = 0; i < attributeProperties.size(); i++)
        {
            AttributeProperty prop = attributeProperties.get(i);
            prop.buffer.position(pos * prop.floatArray.length);
        }

        this.initSize = this.size;

        for (int i = pos; i < pos + size; i++)
        {
            this.moveFloat(this.vertBuffer, 3, 0, i);
            this.moveFloat(this.vertBuffer, 3, 1, i);
            this.moveFloat(this.vertBuffer, 3, 2, i);

            this.moveFloat(this.colBuffer, 4, 0, i);
            this.moveFloat(this.colBuffer, 4, 1, i);
            this.moveFloat(this.colBuffer, 4, 2, i);
            this.moveFloat(this.colBuffer, 4, 3, i);

            for (int j = 0; j < attributeProperties.size(); j++)
            {
                AttributeProperty prop = attributeProperties.get(j);
                for (int f = 0; f < prop.floatArray.length; f++)
                {
                    this.moveFloat(prop.buffer, prop.floatArray.length, f, i);
                }
            }

            this.size++;
        }

        bufferProp.startPoint = initSize;
        this.modifyingSize = -1;

        this.vertBuffer.rewind();
        this.colBuffer.rewind();

        GL15.glBindBuffer(GL15.GL_ARRAY_BUFFER, vertVBO);
        GL15.glBufferSubData(GL15.GL_ARRAY_BUFFER, (long) Float.BYTES * pos * 3, new float[3 * size]);
        GL15.glBindBuffer(GL15.GL_ARRAY_BUFFER, colVBO);
        GL15.glBufferSubData(GL15.GL_ARRAY_BUFFER, (long) Float.BYTES * pos * 4, new float[4 * size]);
        for (int i = 0; i < attributeProperties.size(); i++)
        {
            AttributeProperty prop = attributeProperties.get(i);
            GL15.glBindBuffer(GL15.GL_ARRAY_BUFFER, prop.vboId);
            GL15.glBufferSubData(GL15.GL_ARRAY_BUFFER, (long) Float.BYTES * pos * prop.floatArray.length, new float[prop.floatArray.length * size]);
        }
    }

    public void addAttribute(ShaderGroup.Attribute attribute)
    {
        int attributeId = this.attributeProperties.size();
        this.attributeToId.put(attribute, attributeId);
        FloatBuffer buffer = BufferUtils.createFloatBuffer(capacity * attribute.count);
        float[] floatArray = new float[attribute.count];
        this.attributeProperties.add(new AttributeProperty(-1, buffer, floatArray));
    }

    public void setAttribute(ShaderGroup.Attribute a, float... floats)
    {
        int attributeId = this.attributeToId.getInt(a);
        AttributeProperty prop = this.attributeProperties.get(attributeId);
        int index = 0;
        for (float f: floats)
        {
            prop.floatArray[index] = f;
            index++;
        }
    }

    public void stage()
    {
        this.initialized = true;

        this.vertVBO = this.window.createVBO();
        this.colVBO = this.window.createVBO();

        for (int i = 0; i < attributeProperties.size(); i++)
        {
            AttributeProperty prop = attributeProperties.get(i);
            prop.vboId = this.window.createVBO();
            prop.buffer.flip();
        }

        this.initSize = this.size;
        this.vertBuffer.flip();
        this.colBuffer.flip();

        if (this.dynamic)
        {
            this.vertBuffer.limit(this.vertBuffer.capacity());
            this.colBuffer.limit(this.colBuffer.capacity());
            this.window.vertexBufferDataDynamic(vertVBO, vertBuffer);
            this.window.vertexBufferDataDynamic(colVBO, colBuffer);

            for (int i = 0; i < attributeProperties.size(); i++)
            {
                AttributeProperty prop = attributeProperties.get(i);
                prop.buffer.limit(prop.buffer.capacity());
                this.window.vertexBufferDataDynamic(prop.vboId, prop.buffer);
            }
        }
        else
        {
            this.window.vertexBufferData(vertVBO, vertBuffer);
            this.window.vertexBufferData(colVBO, colBuffer);

            for (int i = 0; i < attributeProperties.size(); i++)
            {
                AttributeProperty prop = attributeProperties.get(i);
                this.window.vertexBufferData(prop.vboId, prop.buffer);
            }
        }
    }

    public void batchDraw()
    {
        this.endModification();
        this.endAdd();

        if (!this.initialized)
            this.stage();

        this.modifying = null;
        if (!this.hidden)
        {
            this.window.setColor(255, 255, 255, 255, this.colorGlow);

            this.shader.setVertexBuffer(vertVBO);
            this.shader.setColorBuffer(colVBO);

            for (ShaderGroup.Attribute a : this.shader.attributes)
            {
                int attributeId = this.attributeToId.getInt(a);
                AttributeProperty prop = this.attributeProperties.get(attributeId);
                this.shader.setCustomBuffer(a, prop.vboId, a.count);
            }

            this.shader.drawVBO(this.size);
        }
    }
}
