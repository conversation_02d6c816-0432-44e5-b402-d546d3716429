package tanks.gui.screen;

import tanks.Drawing;
import tanks.Game;
import tanks.gui.Button;
import tanks.gui.InputSelector;

public class ScreenControlsEditor extends Screen
{
    public static int page = 0;
    public static final int page_count = 7;

    InputSelector pause = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 225, 700, 40, "Editor menu", Game.game.input.editorPause);
    InputSelector objectMenu = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 135, 700, 40, "Object menu", Game.game.input.editorObjectMenu);
    InputSelector play = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 45, 700, 40, "Play level", Game.game.input.editorPlay);
    InputSelector toggleControls = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 45, 700, 40, "Toggle on-screen buttons", Game.game.input.editorToggleControls);
    InputSelector undo = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 135, 700, 40, "Undo", Game.game.input.editorUndo);
    InputSelector redo = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 225, 700, 40, "Redo", Game.game.input.editorRedo);

    InputSelector use = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 225, 700, 40, "Use tool", Game.game.input.editorUse);
    InputSelector action = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 135, 700, 40, "Tool quick action", Game.game.input.editorAction);
    InputSelector team = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 45, 700, 40, "Tank team", Game.game.input.editorTeam);
    InputSelector rotate = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 45, 700, 40, "Tank orientation", Game.game.input.editorRotate);
    InputSelector height = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 135, 700, 40, "Obstacle height", Game.game.input.editorHeight);
    InputSelector group = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 225, 700, 40, "Obstacle group ID", Game.game.input.editorGroupID);

    InputSelector build = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 225, 700, 40, "Build", Game.game.input.editorBuild);
    InputSelector erase = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 135, 700, 40, "Erase", Game.game.input.editorErase);
    InputSelector camera = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 45, 700, 40, "Adjust camera", Game.game.input.editorCamera);
    InputSelector zoomIn = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 45, 700, 40, "Zoom in", Game.game.input.editorZoomIn);
    InputSelector zoomOut = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 135, 700, 40, "Zoom out", Game.game.input.editorZoomOut);
    InputSelector revertCamera = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 225, 700, 40, "Re-center camera", Game.game.input.editorRevertCamera);

    InputSelector nextObj = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 225, 700, 40, "Next object", Game.game.input.editorNextObj);
    InputSelector prevObj = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 135, 700, 40, "Previous object", Game.game.input.editorPrevObj);
    InputSelector nextType = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 45, 700, 40, "Next object type", Game.game.input.editorNextType);
    InputSelector prevType = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 45, 700, 40, "Previous object type", Game.game.input.editorPrevType);
    InputSelector nextMeta = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 135, 700, 40, "Next object property", Game.game.input.editorNextMeta);
    InputSelector prevMeta = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 225, 700, 40, "Previous object property", Game.game.input.editorPrevMeta);

    InputSelector select = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 225, 700, 40, "Select", Game.game.input.editorSelect);
    InputSelector deselect = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 135, 700, 40, "Clear selection", Game.game.input.editorDeselect);
    InputSelector holdSquare = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 45, 700, 40, "Square selection", Game.game.input.editorHoldSquare);
    InputSelector lockSquare = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 45, 700, 40, "Toggle square selection", Game.game.input.editorLockSquare);
    InputSelector toggleAdd = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 135, 700, 40, "Toggle remove from selection", Game.game.input.editorSelectAddToggle);
    InputSelector quickPick = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 225, 700, 40, "Block/tank picker", Game.game.input.editorPickBlock);

    InputSelector copy = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 225, 700, 40, "Copy", Game.game.input.editorCopy);
    InputSelector cut = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 135, 700, 40, "Cut", Game.game.input.editorCut);
    InputSelector paste = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 45, 700, 40, "Paste", Game.game.input.editorPaste);
    InputSelector flipH = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 45, 700, 40, "Flip selection horizontally", Game.game.input.editorFlipHoriz);
    InputSelector flipV = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 135, 700, 40, "Flip selection vertically", Game.game.input.editorFlipVert);
    InputSelector rotateSel = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 225, 700, 40, "Rotate selection", Game.game.input.editorRotateClockwise);

    InputSelector resetTool = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 225, 700, 40, "Clear tool", Game.game.input.editorResetTool);
    InputSelector square = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 135, 700, 40, "Square tool", Game.game.input.editorSquare);
    InputSelector circle = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 45, 700, 40, "Circle tool", Game.game.input.editorCircle);
    InputSelector line = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 45, 700, 40, "Line tool", Game.game.input.editorLine);
    InputSelector wand = new InputSelector(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 135, 700, 40, "Wand tool", Game.game.input.editorWand);


    Button next = new Button(Drawing.drawing.interfaceSizeX * 2 / 3 + 190, Drawing.drawing.interfaceSizeY / 2 + 350, this.objWidth, this.objHeight, "Next page", () -> page++);

    Button previous = new Button(Drawing.drawing.interfaceSizeX * 2 / 3 - 190, Drawing.drawing.interfaceSizeY / 2 + 350, this.objWidth, this.objHeight, "Previous page", () -> page--);

    public ScreenControlsEditor()
    {
        this.music = "menu_options.ogg";
        this.musicID = "menu";

        next.enabled = page < page_count - 1;
        previous.enabled = page > 0;

        this.next.image = "icons/forward.png";
        this.next.imageSizeX = 25;
        this.next.imageSizeY = 25;
        this.next.imageXOffset = 145;

        this.previous.image = "icons/back.png";
        this.previous.imageSizeX = 25;
        this.previous.imageSizeY = 25;
        this.previous.imageXOffset = -145;
    }

    @Override
    public void update()
    {
        if (page == 0)
        {
            pause.update();
            objectMenu.update();
            play.update();
            toggleControls.update();
            undo.update();
            redo.update();
        }
        else if (page == 1)
        {
            use.update();
            action.update();
            team.update();
            rotate.update();
            height.update();
            group.update();
        }
        else if (page == 2)
        {
            build.update();
            erase.update();
            camera.update();
            zoomIn.update();
            zoomOut.update();
            revertCamera.update();
        }
        else if (page == 3)
        {
            nextObj.update();
            prevObj.update();
            nextType.update();
            prevType.update();
            nextMeta.update();
            prevMeta.update();
        }
        else if (page == 4)
        {
            select.update();
            deselect.update();
            holdSquare.update();
            lockSquare.update();
            toggleAdd.update();
            quickPick.update();
        }
        else if (page == 5)
        {
            copy.update();
            cut.update();
            paste.update();
            flipV.update();
            flipH.update();
            rotateSel.update();
        }
        else if (page == 6)
        {
            resetTool.update();
            circle.update();
            square.update();
            line.update();
            wand.update();
        }

        next.enabled = page < page_count - 1;
        previous.enabled = page > 0;

        next.update();
        previous.update();

        ScreenOverlayControls.overlay.update();
    }

    @Override
    public void draw()
    {
        this.drawDefaultBackground();

        if (page == 0)
        {
            redo.draw();
            undo.draw();
            toggleControls.draw();
            play.draw();
            objectMenu.draw();
            pause.draw();

        }
        else if (page == 1)
        {
            group.draw();
            height.draw();
            rotate.draw();
            team.draw();
            action.draw();
            use.draw();
        }
        else if (page == 2)
        {
            revertCamera.draw();
            zoomOut.draw();
            zoomIn.draw();
            camera.draw();
            erase.draw();
            build.draw();
        }
        else if (page == 3)
        {
            prevMeta.draw();
            nextMeta.draw();
            prevType.draw();
            nextType.draw();
            prevObj.draw();
            nextObj.draw();
        }
        else if (page == 4)
        {
            toggleAdd.draw();
            lockSquare.draw();
            holdSquare.draw();
            deselect.draw();
            select.draw();
            quickPick.draw();
        }
        else if (page == 5)
        {
            copy.draw();
            cut.draw();
            paste.draw();
            flipV.draw();
            flipH.draw();
            rotateSel.draw();
        }
        else if (page == 6)
        {
            resetTool.draw();
            circle.draw();
            square.draw();
            line.draw();
            wand.draw();
        }

        next.draw();
        previous.draw();

        Drawing.drawing.setInterfaceFontSize(this.textSize);
        Drawing.drawing.setColor(0, 0, 0);
        Drawing.drawing.displayInterfaceText(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 + 310, "Page %d of %d", (page + 1), page_count);
        Drawing.drawing.setInterfaceFontSize(this.titleSize);
        Drawing.drawing.displayInterfaceText(Drawing.drawing.interfaceSizeX * 2 / 3, Drawing.drawing.interfaceSizeY / 2 - 350, "Editor controls");

        ScreenOverlayControls.overlay.draw();
    }

}
